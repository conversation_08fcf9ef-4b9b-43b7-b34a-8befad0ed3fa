Index: src/comatestack/MCP/MCPCreate/BaseContent.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Form} from 'antd';\nimport {useMCPWorkspaceId} from '@/components/MCP/hooks';\nimport DescriptionField from '../MCPEdit/BasicInfoContent/DescriptionField';\nimport ProtocolField from '../MCPEdit/BasicInfoContent/ProtocolField';\nimport SceneField from '../MCPEdit/BasicInfoContent/SceneField';\nimport ServerConfigField from '../MCPEdit/BasicInfoContent/ServerConfigField';\nimport EnglishIdentifierField from './EnglishIdentifierField';\nimport GlobalVariableField from './GlobalVariableField';\nimport MCPIdentifierField from './MCPIdentifierField';\nimport ServiceNameField from './ServiceNameField';\nimport Overview from './Overview';\nimport AuthDescription from './OpenApiFields/AuthDescriptionField';\nimport MCPSpaceField from './MCPSpaceField';\n\ninterface Props {\n    mode: string;\n    hidden?: boolean;\n}\n\nconst BaseContent = ({mode, hidden}: Props) => {\n    const spaceId = useMCPWorkspaceId();\n    return (\n        <div style={{display: hidden ? 'none' : 'block'}}>\n            <ServiceNameField />\n            {!spaceId && <MCPSpaceField />}\n            <EnglishIdentifierField />\n            <MCPIdentifierField />\n            <DescriptionField />\n            {/* <VisibilityField /> */}\n            <SceneField />\n            <ProtocolField />\n            {(mode === 'openapi' || mode === 'script') && (\n                <Form.Item label=\"全局变量\" name={['serverParams']}>\n                    <GlobalVariableField path={['serverParams']} />\n                </Form.Item>\n            )}\n            {mode === 'openapi' && (\n                <AuthDescription />\n            )}\n            {\n                mode === 'external' && (\n                    <>\n                        <ServerConfigField />\n                    </>\n                )\n            }\n            <Overview />\n        </div>\n    );\n};\n\nexport default BaseContent;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPCreate/BaseContent.tsx b/src/comatestack/MCP/MCPCreate/BaseContent.tsx
--- a/src/comatestack/MCP/MCPCreate/BaseContent.tsx	(revision 40014631b6ada8ca9ac77c25e7efae37fb5116da)
+++ b/src/comatestack/MCP/MCPCreate/BaseContent.tsx	(date 1755230952596)
@@ -6,10 +6,12 @@
 import ServerConfigField from '../MCPEdit/BasicInfoContent/ServerConfigField';
 import EnglishIdentifierField from './EnglishIdentifierField';
 import GlobalVariableField from './GlobalVariableField';
+import EnhancedGlobalVariableField from './EnhancedGlobalVariableField';
 import MCPIdentifierField from './MCPIdentifierField';
 import ServiceNameField from './ServiceNameField';
 import Overview from './Overview';
 import AuthDescription from './OpenApiFields/AuthDescriptionField';
+import AuthTypeField from './OpenApiFields/AuthTypeField';
 import MCPSpaceField from './MCPSpaceField';
 
 interface Props {
@@ -29,14 +31,21 @@
             {/* <VisibilityField /> */}
             <SceneField />
             <ProtocolField />
+            {mode === 'openapi' && (
+                <>
+                    <AuthTypeField />
+                    <AuthDescription />
+                </>
+            )}
             {(mode === 'openapi' || mode === 'script') && (
                 <Form.Item label="全局变量" name={['serverParams']}>
-                    <GlobalVariableField path={['serverParams']} />
+                    {mode === 'openapi' ? (
+                        <EnhancedGlobalVariableField path={['serverParams']} />
+                    ) : (
+                        <GlobalVariableField path={['serverParams']} />
+                    )}
                 </Form.Item>
             )}
-            {mode === 'openapi' && (
-                <AuthDescription />
-            )}
             {
                 mode === 'external' && (
                     <>
Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># See https://help.github.com/articles/ignoring-files/ for more about ignoring files.\n\n# dependencies\n/node_modules\n/.pnp\n.pnp.js\n\n# testing\n/coverage\n\n# production\n/build\n/output\n/.comate-f2c\n\n# misc\n.DS_Store\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision 40014631b6ada8ca9ac77c25e7efae37fb5116da)
+++ b/.gitignore	(date 1755228759084)
@@ -23,3 +23,25 @@
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
+.idea/vcs.xml
+.idea/workspace.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+.idea/shelf/Changes.xml
+.idea/shelf/Changes1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_10_42__Changes_.xml
+.idea/shelf/Changes/shelved.patch
+.idea/shelf/Changes1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/cardBg.png
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_10_42_\[Changes]/shelved.patch
Index: src/comatestack/MCP/MCPCreate/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* eslint-disable max-lines */\nimport styled from '@emotion/styled';\nimport {Form, Space} from 'antd';\nimport {useCallback, useMemo, useState} from 'react';\nimport {Button, message} from '@panda-design/components';\nimport {useSearchParams} from '@panda-design/router';\nimport {useNavigate} from 'react-router-dom';\nimport {useBoolean} from 'huse';\nimport {Gap} from '@/design/iplayground/Gap';\nimport {MCPServerType} from '@/types/mcp/mcp';\nimport {MCPEditLink, MCPSpaceLink} from '@/links/mcp';\nimport BaseContent from './BaseContent';\nimport ImportField from './OpenApiFields/ImportField';\nimport {useHandleCreateMCP} from './hooks';\n\nconst WrapperForm = styled(Form)`\n    padding: 24px 16px !important;\n    min-height: calc(100vh - 48px);\n    max-width: 900px;\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n`;\nconst StyledTitle = styled.h2`\n    color: #000;\n    font-size: 24px;\n    font-style: normal;\n    font-weight: 600;\n    line-height: 36px;\n    margin: 0;\n`;\n\nconst StyledButton = styled(Button)`\n    color: #317ff5;\n    border: 1px solid #317ff5;\n`;\n\nconst ContentWrapper = styled.div`\n    max-height: calc(100vh - 180px);\n    padding-right: 16px;\n    width: 100%;\n    flex-grow: 1;\n    overflow-y: auto;\n`;\n\nconst modeTransfer: Record<MCPServerType, string> = {\n    external: '已有MCP',\n    openapi: 'OpenAPI转MCP',\n    script: '脚本转MCP',\n};\n\nconst MCPCreate = () => {\n    const [form] = Form.useForm();\n    const [loading, {on, off}] = useBoolean();\n    const [stepNum, setStepNum] = useState(1);\n    const {type: mode} = useSearchParams();\n    const navigate = useNavigate();\n    const createMCP = useHandleCreateMCP(form);\n\n    const initialValues = useMemo(\n        () => {\n            return {\n                serverSourceType: mode,\n                serverConf: {\n                    serverSourceType: mode,\n                },\n                ...(mode === 'openapi' ? {\n                    serverProtocolType: 'Streamable_HTTP',\n                } : (mode === 'script' ? {\n                    serverProtocolType: 'STDIO',\n                } : {\n                    serverProtocolType: 'Streamable_HTTP',\n                })),\n                ...(mode !== 'external' ? {\n                    import: {\n                        type: 'openapi',\n                    },\n                } : {}),\n                ...(mode === 'external' ? {\n                    serverConfig: '{\"mcpServers\": {}}',\n                } : {}),\n            };\n        },\n        [mode]\n    );\n\n    const onFinish = useCallback(\n        async (type: 'done' | 'toTool', mode: MCPServerType) => {\n            on();\n            createMCP(\n                mcp => {\n                    if (mcp.id) {\n                        off();\n                        message.success('创建成功');\n                        if (type === 'done') {\n                            const spaceLink = MCPSpaceLink.toUrl({workspaceId: mcp.workspaceId});\n                            navigate(spaceLink);\n                        } else {\n                            const editLink = MCPEditLink.toUrl({\n                                workspaceId: mcp.workspaceId,\n                                mcpId: mcp.id,\n                                activeTab: 'tools',\n                            });\n                            navigate(editLink);\n                        }\n                    } else {\n                        if ((mcp as any).response.data.code === 500) {\n                            message.error((mcp as any).response.data.msg || '创建失败');\n                        }\n                        off();\n                    }\n                },\n                off,\n                mode\n            );\n        },\n        [createMCP, navigate, off, on]\n    );\n    const nextStep = () => {\n        setStepNum(stepNum + 1);\n    };\n    const prevStep = () => {\n        setStepNum(stepNum - 1);\n    };\n    const handleCancel = useCallback(\n        () => {\n            form.resetFields();\n            navigate(-1);\n        },\n        [form, navigate]\n    );\n\n    const onValuesChange = useCallback(\n        (changedValues: any) => {\n            const worksapceIdChanged = 'workspaceId' in changedValues;\n            const labelsChanged = 'labels' in changedValues;\n            // label是跟着workspaceid走的。但如果俩同时变了，那肯定是有业务上自己的调用，就不走这个逻辑了\n            if (worksapceIdChanged && !labelsChanged) {\n                form.setFieldValue('labels', []);\n            }\n        },\n        [form]\n    );\n\n    const done = useCallback(\n        () => {\n            onFinish('done', mode as MCPServerType);\n        },\n        [onFinish, mode]\n    );\n\n    const toTool = useCallback(\n        () => {\n            onFinish('toTool', mode as MCPServerType);\n        },\n        [onFinish, mode]\n    );\n\n    return (\n        <WrapperForm\n            form={form}\n            colon={false}\n            labelCol={{flex: '120px'}}\n            labelAlign=\"left\"\n            style={{width: '100%'}}\n            initialValues={initialValues}\n            onValuesChange={onValuesChange}\n        >\n            <StyledTitle>\n                注册MCP\n                <span style={{color: '#8f8f8f'}}>（{modeTransfer[mode as MCPServerType]}）</span>\n            </StyledTitle>\n            <ContentWrapper>\n                <Gap />\n                <BaseContent mode={mode} hidden={stepNum !== 1} />\n                {mode === 'openapi' && <ImportField path={['serverConf']} hidden={stepNum !== 2} />}\n                <Form.Item name=\"serverSourceType\" hidden />\n                <Form.Item name={['serverConf', 'serverSourceType']} hidden />\n                {/* {mode === 'script' && <BashToolsField hidden={stepNum !== 2} />} */}\n            </ContentWrapper>\n            <Space>\n                {(mode === 'openapi' ? stepNum === 2 : true) && (\n\n                    <Button type=\"primary\" onClick={done} loading={loading}>完成</Button>\n                )}\n                {(mode === 'openapi' ? stepNum === 2 : true) && (\n                    <StyledButton onClick={toTool} loading={loading}>去配置工具</StyledButton>\n                )}\n                {\n                    mode === 'openapi' && (\n                        <>\n                            {stepNum === 1 && <Button onClick={nextStep} type=\"primary\">下一步</Button>}\n                            {stepNum === 2 && <Button onClick={prevStep}>上一步</Button>}\n                        </>\n                    )}\n                <Button onClick={handleCancel}>取消</Button>\n            </Space>\n        </WrapperForm>\n    );\n};\nexport default MCPCreate;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPCreate/index.tsx b/src/comatestack/MCP/MCPCreate/index.tsx
--- a/src/comatestack/MCP/MCPCreate/index.tsx	(revision 40014631b6ada8ca9ac77c25e7efae37fb5116da)
+++ b/src/comatestack/MCP/MCPCreate/index.tsx	(date 1755229711679)
@@ -63,6 +63,11 @@
                 serverSourceType: mode,
                 serverConf: {
                     serverSourceType: mode,
+                    ...(mode === 'openapi' ? {
+                        serverExtension: {
+                            serverAuthType: 'NONE',
+                        },
+                    } : {}),
                 },
                 ...(mode === 'openapi' ? {
                     serverProtocolType: 'Streamable_HTTP',
Index: src/comatestack/MCP/MCPCreate/OpenApiFields/AuthDescriptionField.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Path} from '@panda-design/path-form';\nimport {Form, Input} from 'antd';\n\ninterface Props {\n    path?: Path;\n}\n\nconst AuthDescription = ({path = []}: Props) => {\n    return (\n        <Form.Item label=\"鉴权方法\" name={[...path, 'serverConf', 'serverExtension', 'authDescription']}>\n            <Input.TextArea placeholder=\"请输入鉴权方法\" autoSize={{minRows: 3}} />\n        </Form.Item>\n    );\n};\n\nexport default AuthDescription;\n\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPCreate/OpenApiFields/AuthDescriptionField.tsx b/src/comatestack/MCP/MCPCreate/OpenApiFields/AuthDescriptionField.tsx
--- a/src/comatestack/MCP/MCPCreate/OpenApiFields/AuthDescriptionField.tsx	(revision 40014631b6ada8ca9ac77c25e7efae37fb5116da)
+++ b/src/comatestack/MCP/MCPCreate/OpenApiFields/AuthDescriptionField.tsx	(date 1755230047261)
@@ -1,14 +1,34 @@
 import {Path} from '@panda-design/path-form';
 import {Form, Input} from 'antd';
+import {useWatch} from 'antd/es/form/Form';
 
 interface Props {
     path?: Path;
 }
 
 const AuthDescription = ({path = []}: Props) => {
+    const form = Form.useFormInstance();
+    const authType = useWatch(['serverConf', 'serverExtension', 'serverAuthType'], form);
+
+    if (authType === 'NONE') {
+        return null;
+    }
+
+    const isRequired = authType === 'CLOUD_INIT_IAM' || authType === 'OTHER';
+    const placeholder = authType === 'OTHER'
+        ? '请说明鉴权的方法以及获取鉴权凭证的方式'
+        : '请输入鉴权方法';
+
     return (
-        <Form.Item label="鉴权方法" name={[...path, 'serverConf', 'serverExtension', 'authDescription']}>
-            <Input.TextArea placeholder="请输入鉴权方法" autoSize={{minRows: 3}} />
+        <Form.Item
+            label="鉴权方法"
+            name={[...path, 'serverConf', 'serverExtension', 'authDescription']}
+            rules={isRequired ? [{required: true, message: '请输入鉴权方法'}] : []}
+        >
+            <Input.TextArea
+                placeholder={placeholder}
+                autoSize={{minRows: 3}}
+            />
         </Form.Item>
     );
 };
Index: src/comatestack/MCP/MCPCreate/GlobalVariableField.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* eslint-disable max-lines */\nimport styled from '@emotion/styled';\nimport {Button} from '@panda-design/components';\nimport {Flex, Form, Input, Select, Space, TableColumnsType} from 'antd';\nimport {useCallback} from 'react';\nimport {Path} from '@panda-design/path-form';\nimport {IconAdd} from '@/icons/lucide';\nimport {IconSubtract} from '@/icons/mcp';\nimport {RequiredTitle, StyledTable} from '../MCPEdit/ToolsContent/ParamList';\n\nconst StyledButton = styled(Button)`\n    color: #317ff5 !important;\n    position: relative;\n    &:hover{\n        color: #317ff5 !important;\n    }\n`;\n\nconst typeSelectOptions = [\n    {label: 'String', value: 'string'},\n    {label: 'Number', value: 'number'},\n    {label: 'Boolean', value: 'boolean'},\n    {label: 'Array', value: 'array'},\n    {label: 'Object', value: 'object'},\n    {label: 'Date', value: 'date'},\n];\n\nconst requiredOptions = [\n    {label: '必需', value: true},\n    {label: '可选', value: false},\n];\n\ninterface Param {\n    name: string;\n    description?: string;\n    dataType: string;\n    required: boolean;\n}\n\ninterface Props {\n    value?: Param[];\n    onChange?: (value: Param[]) => void;\n    path: Path;\n    rowKey?: string;\n}\n\n\nconst GlobalVariableField = ({value, onChange, path, rowKey = 'key'}: Props) => {\n    const getNewName = useCallback(\n        () => {\n            const names = value?.map(item => item.name);\n            let index = 1;\n            let name = `key${index}`;\n            while (names?.includes(name)) {\n                index += 1;\n                name = `key${index}`;\n            }\n            return name;\n        },\n        [value]\n    );\n    const onAdd = useCallback(\n        () => {\n            const name = getNewName();\n            // 这里必须要有key,key只能用index，不然和serverConfig里的值对不上\n            // @ts-ignore\n            onChange?.([...(value || []), {name, key: value?.length ?? 0, dataType: 'string', required: false}]);\n        },\n        [onChange, value, getNewName]\n    );\n\n    const onDelete = useCallback(\n        (index: number) => {\n            onChange?.([...value.slice(0, index), ...value.slice(index + 1)]);\n        },\n        [onChange, value]\n    );\n\n    const columns: TableColumnsType<Param> = [\n        {\n            title: <RequiredTitle>变量名称</RequiredTitle>,\n            dataIndex: 'name',\n            width: 200,\n            render: (_, record, index) => (\n                <Space>\n                    <Button icon={<IconSubtract />} tooltip=\"删除\" type=\"text\" onClick={() => onDelete(index)} />\n                    <Form.Item\n                        style={{marginBottom: 0}}\n                        name={[...path, index, 'name']}\n                        rules={[{required: true, message: '必填项，不可为空'}]}\n                    >\n                        <Input placeholder=\"请输入变量名称\" />\n                    </Form.Item>\n                </Space>\n            ),\n        },\n        {\n            title: <RequiredTitle>是否必须</RequiredTitle>,\n            dataIndex: 'required',\n            width: 100,\n            render: (_, record, index) => (\n                <Form.Item\n                    style={{marginBottom: 0}}\n                    name={[...path, index, 'required']}\n                    rules={[{required: true, message: '必填项，不可为空'}]}\n                >\n                    <Select options={requiredOptions} allowClear placeholder=\"请选择\" />\n                </Form.Item>\n            ),\n        },\n        {\n            title: <RequiredTitle>类型</RequiredTitle>,\n            dataIndex: 'dataType',\n            width: 120,\n            render: (_, record, index) => (\n                <Form.Item\n                    style={{marginBottom: 0}}\n                    name={[...path, index, 'dataType']}\n                    rules={[{required: true, message: '必填项，不可为空'}]}\n                >\n                    <Select options={typeSelectOptions} allowClear placeholder=\"请选择\" />\n                </Form.Item>\n            ),\n        },\n        {\n            title: '描述',\n            dataIndex: 'description',\n            render: (_, record, index) => (\n                <Form.Item\n                    style={{marginBottom: 0}}\n                    name={[...path, index, 'description']}\n                    rules={[{max: 50, message: '最长为50字符'}]}\n                >\n                    <Input maxLength={50} placeholder=\"请输入描述\" />\n                </Form.Item>\n            ),\n        },\n\n    ];\n    return (\n        <Space direction=\"vertical\" style={{width: '100%'}}>\n            <Flex justify=\"space-between\" align=\"center\">\n                <StyledButton type=\"text\" icon={<IconAdd />} onClick={onAdd}>添加变量</StyledButton>\n                <span style={{color: '#2D2D2D', fontSize: 12}}>调用该MCP的工具时需传入的全局变量，不会暴露给模型，在脚本中以环境变量形式引用</span>\n            </Flex>\n            <StyledTable\n                rowKey={rowKey}\n                dataSource={value}\n                pagination={{hideOnSinglePage: true}}\n                columns={columns}\n            />\n        </Space>\n    );\n};\n\nexport default GlobalVariableField;\n\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPCreate/GlobalVariableField.tsx b/src/comatestack/MCP/MCPCreate/GlobalVariableField.tsx
--- a/src/comatestack/MCP/MCPCreate/GlobalVariableField.tsx	(revision 40014631b6ada8ca9ac77c25e7efae37fb5116da)
+++ b/src/comatestack/MCP/MCPCreate/GlobalVariableField.tsx	(date 1755236909168)
@@ -2,7 +2,7 @@
 import styled from '@emotion/styled';
 import {Button} from '@panda-design/components';
 import {Flex, Form, Input, Select, Space, TableColumnsType} from 'antd';
-import {useCallback} from 'react';
+import {useCallback, useMemo} from 'react';
 import {Path} from '@panda-design/path-form';
 import {IconAdd} from '@/icons/lucide';
 import {IconSubtract} from '@/icons/mcp';
@@ -35,20 +35,49 @@
     description?: string;
     dataType: string;
     required: boolean;
+    isSystem?: boolean;
+    hasConflict?: boolean;
+    key?: string;
 }
 
 interface Props {
     value?: Param[];
     onChange?: (value: Param[]) => void;
     path: Path;
-    rowKey?: string;
+    systemVariables?: Param[];
 }
 
 
-const GlobalVariableField = ({value, onChange, path, rowKey = 'key'}: Props) => {
+const GlobalVariableField = ({value, onChange, path, systemVariables = []}: Props) => {
+    const mergedVariables = useMemo(
+        () => {
+            const userVariables = value || [];
+            const systemVarNames = systemVariables.map(v => v.name);
+
+            const processedSystemVars = systemVariables.map((sysVar, index) => ({
+                ...sysVar,
+                key: `system_${sysVar.name}_${index}`,
+            }));
+
+            const processedUserVars = userVariables.map((userVar, index) => {
+                // 确保每个用户变量都有唯一的 key
+                if (!userVar.key) {
+                    userVar.key = `user_var_${Date.now()}_${Math.random().toString(36).substring(2, 11)}_${index}`;
+                }
+                return {
+                    ...userVar,
+                    hasConflict: systemVarNames.includes(userVar.name),
+                };
+            });
+
+            return [...processedSystemVars, ...processedUserVars];
+        },
+        [value, systemVariables]
+    );
+
     const getNewName = useCallback(
         () => {
-            const names = value?.map(item => item.name);
+            const names = mergedVariables?.map(item => item.name);
             let index = 1;
             let name = `key${index}`;
             while (names?.includes(name)) {
@@ -57,83 +86,176 @@
             }
             return name;
         },
-        [value]
+        [mergedVariables]
     );
     const onAdd = useCallback(
         () => {
             const name = getNewName();
-            // 这里必须要有key,key只能用index，不然和serverConfig里的值对不上
-            // @ts-ignore
-            onChange?.([...(value || []), {name, key: value?.length ?? 0, dataType: 'string', required: false}]);
+            const newItem = {
+                name,
+                dataType: 'string',
+                required: false,
+                key: `user_var_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
+            };
+            onChange?.([...(value || []), newItem]);
         },
         [onChange, value, getNewName]
     );
-
     const onDelete = useCallback(
-        (index: number) => {
-            onChange?.([...value.slice(0, index), ...value.slice(index + 1)]);
+        (mergedIndex: number) => {
+            const itemToDelete = mergedVariables[mergedIndex];
+
+            if (itemToDelete?.isSystem) {
+                return;
+            }
+
+            const userVariables = value || [];
+
+            // 通过 key 找到要删除的用户变量
+            const userIndex = userVariables.findIndex(userVar => {
+                // 如果有 key，优先使用 key 匹配
+                if (itemToDelete.key && userVar.key) {
+                    return userVar.key === itemToDelete.key;
+                }
+                // 否则使用 name 匹配（兼容没有 key 的情况）
+                return userVar.name === itemToDelete.name;
+            });
+
+            if (userIndex !== -1) {
+                const newValue = [...userVariables];
+                newValue.splice(userIndex, 1);
+                onChange?.(newValue);
+            }
         },
-        [onChange, value]
+        [onChange, value, mergedVariables]
     );
+
 
     const columns: TableColumnsType<Param> = [
         {
             title: <RequiredTitle>变量名称</RequiredTitle>,
             dataIndex: 'name',
             width: 200,
-            render: (_, record, index) => (
-                <Space>
-                    <Button icon={<IconSubtract />} tooltip="删除" type="text" onClick={() => onDelete(index)} />
-                    <Form.Item
-                        style={{marginBottom: 0}}
-                        name={[...path, index, 'name']}
-                        rules={[{required: true, message: '必填项，不可为空'}]}
-                    >
-                        <Input placeholder="请输入变量名称" />
-                    </Form.Item>
-                </Space>
-            ),
+            render: (_, record, index) => {
+                const isSystemVar = record.isSystem;
+                const hasConflict = record.hasConflict;
+                const actualIndex = isSystemVar ? index : index - systemVariables.length;
+
+                return (
+                    <div>
+                        <Flex align="center" gap={8}>
+                            <Button
+                                icon={<IconSubtract />}
+                                tooltip={isSystemVar ? '系统变量不可删除' : '删除'}
+                                type="text"
+                                onClick={() => onDelete(index)}
+                                disabled={isSystemVar}
+                                style={{minWidth: '32px', width: '32px'}}
+                            />
+                            <div style={{flex: 1}}>
+                                {isSystemVar ? (
+                                    <Input
+                                        value={record.name}
+                                        disabled
+                                        style={{color: '#666'}}
+                                    />
+                                ) : (
+                                    <Form.Item
+                                        style={{marginBottom: 0}}
+                                        name={[...path, actualIndex, 'name']}
+                                        rules={[{required: true, message: '必填项，不可为空'}]}
+                                        validateStatus={hasConflict ? 'error' : ''}
+                                    >
+                                        <Input
+                                            placeholder="请输入变量名称"
+                                            style={hasConflict ? {borderColor: '#ff4d4f', color: '#ff4d4f'} : {}}
+                                        />
+                                    </Form.Item>
+                                )}
+                            </div>
+                        </Flex>
+                        {hasConflict && !isSystemVar && (
+                            <div style={{color: '#ff4d4f', fontSize: '12px', marginTop: '4px', marginLeft: '40px'}}>
+                                与系统变量冲突
+                            </div>
+                        )}
+                    </div>
+                );
+            },
         },
         {
             title: <RequiredTitle>是否必须</RequiredTitle>,
             dataIndex: 'required',
             width: 100,
-            render: (_, record, index) => (
-                <Form.Item
-                    style={{marginBottom: 0}}
-                    name={[...path, index, 'required']}
-                    rules={[{required: true, message: '必填项，不可为空'}]}
-                >
-                    <Select options={requiredOptions} allowClear placeholder="请选择" />
-                </Form.Item>
-            ),
+            render: (_, record, index) => {
+                const isSystemVar = record.isSystem;
+                const actualIndex = isSystemVar ? index : index - systemVariables.length;
+
+                return isSystemVar ? (
+                    <Select
+                        value={record.required}
+                        disabled
+                        options={requiredOptions}
+                    />
+                ) : (
+                    <Form.Item
+                        style={{marginBottom: 0}}
+                        name={[...path, actualIndex, 'required']}
+                        rules={[{required: true, message: '必填项，不可为空'}]}
+                    >
+                        <Select options={requiredOptions} allowClear placeholder="请选择" />
+                    </Form.Item>
+                );
+            },
         },
         {
             title: <RequiredTitle>类型</RequiredTitle>,
             dataIndex: 'dataType',
             width: 120,
-            render: (_, record, index) => (
-                <Form.Item
-                    style={{marginBottom: 0}}
-                    name={[...path, index, 'dataType']}
-                    rules={[{required: true, message: '必填项，不可为空'}]}
-                >
-                    <Select options={typeSelectOptions} allowClear placeholder="请选择" />
-                </Form.Item>
-            ),
+            render: (_, record, index) => {
+                const isSystemVar = record.isSystem;
+                const actualIndex = isSystemVar ? index : index - systemVariables.length;
+
+                return isSystemVar ? (
+                    <Select
+                        value={record.dataType}
+                        disabled
+                        options={typeSelectOptions}
+                    />
+                ) : (
+                    <Form.Item
+                        style={{marginBottom: 0}}
+                        name={[...path, actualIndex, 'dataType']}
+                        rules={[{required: true, message: '必填项，不可为空'}]}
+                    >
+                        <Select options={typeSelectOptions} allowClear placeholder="请选择" />
+                    </Form.Item>
+                );
+            },
         },
         {
             title: '描述',
             dataIndex: 'description',
-            render: (_, record, index) => (
-                <Form.Item
-                    style={{marginBottom: 0}}
-                    name={[...path, index, 'description']}
-                    rules={[{max: 50, message: '最长为50字符'}]}
-                >
-                    <Input maxLength={50} placeholder="请输入描述" />
-                </Form.Item>
-            ),
+            render: (_, record, index) => {
+                const isSystemVar = record.isSystem;
+                const actualIndex = isSystemVar ? index : index - systemVariables.length;
+
+                return isSystemVar ? (
+                    <Input
+                        value={record.description}
+                        disabled
+                        maxLength={50}
+                    />
+                ) : (
+                    <Form.Item
+                        style={{marginBottom: 0}}
+                        name={[...path, actualIndex, 'description']}
+                        rules={[{max: 50, message: '最长为50字符'}]}
+                    >
+                        <Input maxLength={50} placeholder="请输入描述" />
+                    </Form.Item>
+                );
+            },
         },
 
     ];
@@ -144,8 +266,8 @@
                 <span style={{color: '#2D2D2D', fontSize: 12}}>调用该MCP的工具时需传入的全局变量，不会暴露给模型，在脚本中以环境变量形式引用</span>
             </Flex>
             <StyledTable
-                rowKey={rowKey}
-                dataSource={value}
+                rowKey="key"
+                dataSource={mergedVariables}
                 pagination={{hideOnSinglePage: true}}
                 columns={columns}
             />
