import {Form, Radio} from 'antd';
import {useCallback} from 'react';

const authTypeOptions = [
    {label: '无需鉴权', value: 'NONE'},
    {label: '百度云度厂版IAM鉴权', value: 'CLOUD_INIT_IAM'},
    {label: '其他', value: 'OTHER'},
];

interface Props {
    onAuthTypeChange?: (authType: string) => void;
}

const AuthTypeField = ({onAuthTypeChange}: Props) => {

    const handleChange = useCallback(
        (e: any) => {
            const value = e.target.value;
            onAuthTypeChange?.(value);
        },
        [onAuthTypeChange]
    );

    return (
        <Form.Item
            label="鉴权方式"
            name={['serverConf', 'serverExtension', 'serverAuthType']}
            rules={[{required: true, message: '请选择鉴权方式'}]}
        >
            <Radio.Group
                options={authTypeOptions}
                onChange={handleChange}
            />
        </Form.Item>
    );
};

export default AuthTypeField;
