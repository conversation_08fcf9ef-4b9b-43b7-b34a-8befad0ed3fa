import {Path} from '@panda-design/path-form';
import {Form, Input} from 'antd';
import {useWatch} from 'antd/es/form/Form';

interface Props {
    path?: Path;
}

const AuthDescription = ({path = []}: Props) => {
    const form = Form.useFormInstance();
    const authType = useWatch(['serverConf', 'serverExtension', 'serverAuthType'], form);

    if (authType === 'NONE') {
        return null;
    }

    const isRequired = authType === 'CLOUD_INIT_IAM' || authType === 'OTHER';
    const placeholder = authType === 'OTHER'
        ? '请说明鉴权的方法以及获取鉴权凭证的方式'
        : '请输入鉴权方法';

    return (
        <Form.Item
            label="鉴权方法"
            name={[...path, 'serverConf', 'serverExtension', 'authDescription']}
            rules={isRequired ? [{required: true, message: '请输入鉴权方法'}] : []}
        >
            <Input.TextArea
                placeholder={placeholder}
                autoSize={{minRows: 3}}
            />
        </Form.Item>
    );
};

export default AuthDescription;

