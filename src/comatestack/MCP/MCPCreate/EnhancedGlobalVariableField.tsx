import {Form} from 'antd';
import {useCallback, useEffect, useMemo} from 'react';
import {useWatch} from 'antd/es/form/Form';
import GlobalVariableField from './GlobalVariableField';

interface Param {
    name: string;
    description?: string;
    dataType: string;
    required: boolean;
    isSystem?: boolean;
}

interface Props {
    path: any[];
}

const BAIDU_CLOUD_SYSTEM_VARIABLES: Param[] = [
    {
        name: 'AccessKey',
        description: '请求时用于生成签名',
        dataType: 'string',
        required: true,
        isSystem: true,
    },
    {
        name: 'SecretKey',
        description: '请求时用于生成签名',
        dataType: 'string',
        required: true,
        isSystem: true,
    },
];

const EnhancedGlobalVariableField = ({path}: Props) => {
    const form = Form.useFormInstance();
    const authType = useWatch(['serverConf', 'serverExtension', 'serverAuthType'], form);
    const currentValue = useWatch(path, form);

    const systemVariables = useMemo(
        () => {
            return authType === 'CLOUD_INIT_IAM' ? BAIDU_CLOUD_SYSTEM_VARIABLES : [];
        },
        [authType]
    );

    const handleVariableChange = useCallback(
        (newValue: Param[]) => {
            form.setFieldValue(path, newValue);
        },
        [form, path]
    );

    useEffect(
        () => {
            if (authType === 'CLOUD_INIT_IAM') {
                const authDescription = '根据AccessKey SecretKey进行认证，获取方式参考 云上百度（百度云度厂版）相关文档 https://cloud.baidu-int.com/icloud/help/%E4%BC%81%E4%B8%9A%E7%BB%84%E7%BB%87/%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/%E5%88%9B%E5%BB%BA%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/';
                form.setFieldValue(['serverConf', 'serverExtension', 'authDescription'], authDescription);
            } else if (authType === 'OTHER' || authType === 'NONE') {
                form.setFieldValue(['serverConf', 'serverExtension', 'authDescription'], '');
            }
        },
        [authType, form]
    );

    return (
        <GlobalVariableField
            path={path}
            value={currentValue}
            onChange={handleVariableChange}
            systemVariables={systemVariables}
        />
    );
};

export default EnhancedGlobalVariableField;
