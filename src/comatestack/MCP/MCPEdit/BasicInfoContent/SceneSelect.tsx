/* eslint-disable max-lines */
import {Checkbox, Flex, Input, Select, Space} from 'antd';
import {useCallback, useMemo, useState, MouseEvent, ReactElement} from 'react';
import styled from '@emotion/styled';
import {useBoolean} from 'huse';
import {Button, IconClose, message} from '@panda-design/components';
import {useEffect} from 'react';
import {groupBy} from 'lodash';
import {IconAdd, IconDelete} from '@/icons/lucide';
import {useSpaceLabels, loadSpaceLabels} from '@/regions/mcp/mcpSpace';
import {apiDeleteSpaceLabel, apiPostSpaceLabel} from '@/api/mcp';
import {filterOption} from '@/utils/iplayground/select';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    &:hover{
        color: #317ff5 !important;
    }
`;

const StyledTag = styled.span`
    display: inline-block;
    background: #EEEEEE;
    border-radius: 2px;
    color: #8F8F8F;
    padding: 0px 6px;
    font-weight: 400 !important;
`;

const SelectedTag = styled.span`
    padding: 2px 8px;
    display: inline-block;
    background: #E5F2FF;
    border-radius: 2px;
    color: #0080FF;
    margin-right: 8px;
    cursor: pointer;
    font-size: 12px;
    svg{
        cursor: pointer;
        margin-left: 4px;
    }

`;

const DropdownWrapper = styled.div`
    position: relative;
    .ant-5-select-item-option-state{
        display: none !important;
    }
`;

const AddArea = styled.div`
    padding: 6px 12px;
    border-top: 1px solid #BFBFBF;
    margin-top: 12px;

`;

const StyledOptionWrapper = styled(Flex)`
    height: 32px;
    button{
        display: none;
    }
    &:hover{
        button{
            display: block;
        }
    }

`;

interface Props {
    value?: number[];
    onChange?: (value: number[]) => void;
    workspaceId: number;
}

// 这里的onChange是为了适配antd form的。
const SceneSelect = ({value, onChange, workspaceId}: Props) => {
    const labels = useSpaceLabels(workspaceId);
    useEffect(
        () => {
            if (labels.length === 0 && (workspaceId)) {
                loadSpaceLabels({workspaceId: workspaceId});
            }
        },
        [labels, workspaceId]
    );

    const [addName, setAddName] = useState<string>('');
    const [showInput, {on, off}] = useBoolean();
    const handleOptions = useMemo(
        () => {
            const groupedLabels = groupBy(labels, 'labelType');
            return ([
                ...groupedLabels?.GLOBAL ?? [],
                ...groupedLabels?.WORKSPACE ?? [],
                ...groupedLabels?.ZONE ?? [],
            ])?.map(
                ({id, labelValue, labelType}) => ({
                    value: id,
                    label: labelValue,
                    labelType,
                })
            );
        },
        [labels]
    );

    const handleAddOption = useCallback(
        async () => {
            if (!addName?.trim()) {
                return message.warning('请输入场景名称');
            }
            if (!workspaceId) {
                message.warning('请先选择工作空间');
                return;
            }
            if (addName && !labels.some(option => option.labelValue === addName)) {
                const label = await apiPostSpaceLabel({workspaceId, labelValue: addName});
                onChange?.([...(value || []), label.id]);
                loadSpaceLabels({workspaceId});
                setAddName('');
                message.success('添加成功');
            } else {
                message.warning('场景名称已存在');
            }
        },
        [addName, labels, onChange, value, workspaceId]
    );

    const handleCancelAdd = useCallback(
        () => {
            setAddName('');
            off();
        },
        [off]
    );
    const handleDelete = useCallback(
        async (e: MouseEvent, index: number) => {
            e.stopPropagation();
            const option = handleOptions[index];
            await apiDeleteSpaceLabel({labelId: option.value});
            loadSpaceLabels({workspaceId});
            message.success('删除成功');
            if (value?.includes(option.value)) {
                onChange(value.filter(id => id !== option.value));
            }
        },
        [handleOptions, onChange, value, workspaceId]
    );

    const optionRender = useCallback(
        (option: any, info: any) => {
            const checked = value?.includes(option.value);
            return (
                <StyledOptionWrapper align="center" justify="space-between">
                    <Space>
                        <Checkbox checked={checked} />
                        <span>{option.label}</span>
                    </Space>
                    {
                        option.data.labelType === 'GLOBAL'
                            ? <StyledTag>内置</StyledTag>
                            : option.data.labelType === 'ZONE' ? <StyledTag>专区</StyledTag>
                                : (
                                    <Button
                                        type="text"
                                        icon={<IconDelete />}
                                        onClick={e => handleDelete(e, info.index as number)}
                                    />
                                )
                    }
                </StyledOptionWrapper>
            );
        },
        [handleDelete, value]
    );

    const dropdownVisibleHandle = useCallback(
        (visible: boolean) => {
            if (!visible) {
                handleCancelAdd();
            }
        },
        [handleCancelAdd]
    );
    const tagRender = useCallback(
        (props: any) => {
            const {label, onClose} = props;
            return (
                <SelectedTag
                    onMouseDown={
                        e => {
                            e.stopPropagation();
                            e.preventDefault();
                        }
                    }
                >
                    {label}
                    <IconClose onClick={onClose} />
                </SelectedTag>
            );
        },
        []
    );
    const dropdownRender = useCallback(
        (menu: ReactElement) => {
            return (
                <DropdownWrapper>
                    {menu}
                    <AddArea>
                        {
                            showInput
                                ? (
                                    <Flex gap={4}>
                                        <Input
                                            onKeyDown={e => {
                                                if (e.keyCode === 8) {
                                                    e.stopPropagation();
                                                }
                                            }}
                                            value={addName}
                                            onChange={e => setAddName(e.target.value)}
                                            placeholder="请输入场景名称"
                                            showCount
                                            maxLength={10}
                                        />
                                        <Button type="text" onClick={handleCancelAdd}>取消</Button>
                                        <StyledButton
                                            type="text"
                                            onClick={handleAddOption}
                                        >
                                            添加
                                        </StyledButton>
                                    </Flex>
                                )
                                : (
                                    <StyledButton
                                        type="text"
                                        icon={<IconAdd />}
                                        onClick={on}
                                    >
                                        添加场景
                                    </StyledButton>
                                )
                        }
                    </AddArea>
                </DropdownWrapper>
            );
        },
        [addName, handleAddOption, handleCancelAdd, on, showInput]
    );

    return (
        <Select
            value={value}
            mode="multiple"
            placeholder="请选择或输入适用场景"
            allowClear
            showSearch
            filterOption={filterOption}
            onChange={onChange}
            options={handleOptions}
            onDropdownVisibleChange={dropdownVisibleHandle}
            tagRender={tagRender}
            optionRender={optionRender}
            dropdownRender={dropdownRender}
        />
    );
};

export default SceneSelect;

